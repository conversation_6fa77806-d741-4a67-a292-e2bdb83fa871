package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/4/12 19:15
 * @verison: 1.0
 * @description:
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiLeaveApplicationFormVo {

    @ApiModelProperty("主键id")
    @JsonIgnore
    private Long id;

//    /**
//     * 公司Id
//     */
//    @ApiModelProperty(value = "公司Id")
//    private Long fkCompanyId;
//
//    /**
//     * 部门Id
//     */
//    @ApiModelProperty(value = "部门Id")
//    private Long fkDepartmentId;
//
//    /**
//     * 办公室Id
//     */
//    @ApiModelProperty(value = "办公室Id")
//    private Long fkOfficeId;

    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    @JsonIgnore
    private Long fkStaffId;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    @JsonIgnore
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 工休申请单编号（系统生成）
     */
    @ApiModelProperty(value = "工休申请单编号（系统生成）")
    @JsonIgnore
    private String num;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 天数，2小时0.25, 4小时0.5, 8小时1天
     */
    @ApiModelProperty(value = "天数，2小时0.25, 4小时0.5, 8小时1天")
    private BigDecimal days;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String reason;


    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private String status;

    /**
     * 工休单类型名称
     */
    @ApiModelProperty(value = "工休单类型名称")
    private String leaveApplicationFormTypeName;

    /**
     * 工休单类型key
     */
    @ApiModelProperty(value = "工休单类型key")
    @JsonIgnore
    private String leaveApplicationFormTypeKey;
    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    @JsonIgnore
    private String assigneeName;

//    /**
//     * 入职时间
//     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @ApiModelProperty("入职时间")
//    private Date entryDate;
//
//    /**
//     * 公司名称
//     */
//    @ApiModelProperty(value = "公司名称")
//    private String companyName;
//
//    /**
//     * 办公室名称
//     */
//    @ApiModelProperty(value = "办公室名称")
//    private String officeName;
//
//    /**
//     * 部门名称
//     */
//    @ApiModelProperty(value = "部门名称")
//    private String departmentName;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    @JsonIgnore
    private String gmtCreateUser;

}
