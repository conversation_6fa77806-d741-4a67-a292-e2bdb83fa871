package com.get.pmpcenter.dto.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.pmpcenter.dto.common.MediaDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:22
 * @Version 1.0
 * 保存合同
 */
@Data
public class SaveProviderContractDto {

    @ApiModelProperty(value = "合同id-编辑传")
    private Long id;

    @ApiModelProperty(value = "合同供应商Id(学校提供商Id)")
    @NotNull(message = "合同供应商Id不能为空")
    private Long institutionProviderId;

    @ApiModelProperty(value = "合同签订方Id")
    @NotNull(message = "合同签订方Id不能为空")
    private Long contractPartyId;

    @ApiModelProperty(value = "合同类型Id")
    private Long contractTypeId;

    @ApiModelProperty(value = "合同名称")
    @NotBlank(message = "合同名称不能为空")
    private String contractTitle;

    @ApiModelProperty(value = "合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "合同开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制(是否长期生效)：0否/1是")
    private Integer isTimeless = 0;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive = 0;

    @ApiModelProperty(value = "合同备注")
    private String remark;

    @ApiModelProperty(value = "合同附件文件集合")
    @NotNull(message = "合同附件文件不能为空")
    private List<MediaDto> mediaList;

    @ApiModelProperty(value = "是否同步方案时间：0否/1是")
    private Integer updatePlanTime = 0;

    @ApiModelProperty(value = "合同KPI描述")
    private String contractKpi;

    @ApiModelProperty(value = "续约状态：0无标记/1续约中/-1不续约")
    private Integer renewalStatus;

    @ApiModelProperty(value = "续约备注")
    private String renewalRemark;

}
