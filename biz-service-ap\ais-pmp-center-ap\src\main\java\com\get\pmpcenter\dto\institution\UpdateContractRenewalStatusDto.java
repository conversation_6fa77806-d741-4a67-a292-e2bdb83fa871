package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/8/22
 * @Version 1.0
 * @apiNote:批量续约
 */
@Data
public class UpdateContractRenewalStatusDto {

    @ApiModelProperty(value = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;

    @ApiModelProperty(value = "续约状态：0无标记/1续约中/-1不续约")
    @NotNull(message = "续约状态不能为空")
    private Integer renewalStatus;

    @ApiModelProperty(value = "续约备注")
    private String renewalRemark;
}
