package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:36
 * @Version 1.0
 */
@Data
@TableName("m_institution_provider_commission_plan")
public class InstitutionProviderCommissionPlan extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商合同Id")
    private Long fkInstitutionProviderContractId;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否全局模板：0否/1是")
    private Integer isGobal;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "佣金方案适用学生说明-英文")
    private String territory;

    @ApiModelProperty(value = "佣金方案适用学生说明-中文")
    private String territoryChn;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-英文")
    private String summary;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-中文")
    private String summaryChn;

    @ApiModelProperty(value = "佣金方案专业说明-英文")
    private String course;

    @ApiModelProperty(value = "佣金方案专业说明（中文）")
    private String courseChn;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "上一版本明细Json")
    private String preVersionJson;

    @ApiModelProperty(value = "是否续约中:0否/1是")
    private Integer isRenewal;

    @ApiModelProperty(value = "续约状态：0无标记/1续约中/-1不续约")
    @TableField(exist = false)
    private Integer renewalStatus;
}