package com.get.pmpcenter.vo.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/21
 * @Version 1.0
 * @apiNote:代理佣金方案列表
 */
@Data
public class AgentCommissionPlanListVo extends AgentCommissionPlanVo {

    @ApiModelProperty(value = "所属公司ID")
    private Long companyId;

    @ApiModelProperty(value = "所属公司编号")
    private String companyNum;

    @ApiModelProperty(value = "绑定的学校列表-字符串")
    private String institutionIds;

    @ApiModelProperty(value = "绑定的学校列表-数组")
    private List<Long> institutionIdList;

    @ApiModelProperty(value = "绑定的学校数量")
    private Integer institutionCount;

    @ApiModelProperty(value = "绑定的第一个学校名称")
    private String institutionName;

    @ApiModelProperty(value = "绑定的第一个学校名称-中文")
    private String institutionNameChn;

    @ApiModelProperty(value = "是否学校提供商佣金明细修改：0否/1是")
    private Integer isInstitutionProviderCommissionModify;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "是否续约中:0否/1是")
    private Integer isRenewal;

    @ApiModelProperty(value = "续约状态：0无标记/1续约中/-1不续约")
    private Integer renewalStatus;

    public void setInstitutionIds(String institutionIds) {
        this.institutionIds = institutionIds;
        if (institutionIds != null && !institutionIds.isEmpty()) {
            this.institutionIdList = Arrays.stream(institutionIds.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
    }
}
