<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionProviderContractMapper">

    <select id="getProviderContractPage" resultType="com.get.pmpcenter.vo.institution.ProviderContractVo">
        select c.id,
        c.fk_institution_provider_id,
        c.fk_contract_party_id,
        c.contract_num,
        c.contract_title,
        c.start_time,
        c.end_time,
        c.is_timeless,
        c.remark,
        c.is_locked,
        c.is_active,
        c.gmt_create_user,
        c.approval_status,
        c.renewal_status,
        c.renewal_remark,
        p.name as contractPartyName,
        p.name_chn as contractPartyNameChn,
        i.name as providerName,
        i.name_chn as providerNameChn
        from m_institution_provider_contract c
        left join m_contract_party p on c.fk_contract_party_id = p.id
        left join ais_institution_center.m_institution_provider i on c.fk_institution_provider_id = i.id
        <where>
            c.fk_institution_provider_id in (
            select fk_institution_provider_id from ais_institution_center.r_institution_provider_company where
            fk_company_id in
            <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
            )
            and c.id in
            <foreach collection="contractIds" item="contractId" separator="," open="(" close=")">
                #{contractId}
            </foreach>
            <if test="param.institutionProviderId != null and param.institutionProviderId > 0">
                AND c.fk_institution_provider_id =
                #{param.institutionProviderId}
            </if>
            <if test="param.isActive != null">
                AND c.is_active =
                #{param.isActive}
            </if>
            <if test="param.contractNum != null and param.contractNum != ''">
                AND c.contract_num LIKE CONCAT('%',
                #{param.contractNum},
                '%'
                )
            </if>
            <if test="param.startTime != null and param.endTime != null">
                AND
                (
                    (c.is_timeless = 0 AND DATE (c.start_time) &gt;= #{param.startTime} AND DATE (c.end_time) &lt;=
                        #{param.endTime})
                        OR (c.is_timeless = 1 AND DATE (c.start_time) &gt;= #{param.startTime}))
            </if>
            <if test="param.contractPartyName != null and param.contractPartyName != ''">
                AND
                ( p.name LIKE CONCAT('%', #{param.contractPartyName}, '%')
                    OR
                    p.name_chn LIKE CONCAT('%', #{param.contractPartyName}, '%'))
            </if>
            <if test="param.institutionProviderName != null and param.institutionProviderName != ''">
                AND
                ( i.name LIKE CONCAT('%', #{param.institutionProviderName}, '%')
                    OR
                    i.name_chn LIKE CONCAT('%', #{param.institutionProviderName}, '%'))
            </if>
            <if test="param.approvalStatus != null">
                AND exists (select 1 from m_institution_provider_commission_plan cp
                    where c.id = cp.fk_institution_provider_contract_id and cp.approval_status =
                #{param.approvalStatus}
                )
            </if>
            <if test="param.createUser != null and param.createUser != ''">
                AND
                ( exists(select 1 from ais_permission_center.m_staff ma where ma.login_id = c.gmt_create_user
                    and (ma.name LIKE CONCAT('%', #{param.createUser}, '%')
                    OR ma.name_en LIKE CONCAT('%', #{param.createUser}, '%')
                    OR ma.login_id = #{param.createUser}))
                    or exists (select 1 from m_institution_provider_commission_plan pcp where pcp.fk_institution_provider_contract_id = c.id
                    and exists (select 1 from ais_permission_center.m_staff ma where ma.login_id = pcp.gmt_create_user
                    and (ma.name LIKE CONCAT('%', #{param.createUser}, '%')
                    OR ma.name_en LIKE CONCAT('%', #{param.createUser}, '%')
                    OR ma.login_id = #{param.createUser}) )))
            </if>
            <if test="param.countryIds != null and param.countryIds.size() > 0">
                and exists(select 1 from m_institution_provider_commission_plan cp where
                cp.fk_institution_provider_contract_id = c.id
                and cp.id in (select distinct(cpi.fk_institution_provider_commission_plan_id) from
                r_institution_provider_commission_plan_institution cpi
                where exists(select 1 from ais_institution_center.m_institution mi where mi.id = cpi.fk_institution_id
                and mi.fk_area_country_id in
                <foreach collection="param.countryIds" item="countryId" separator="," open="(" close=")">
                    #{countryId}
                </foreach>
                )
                )
                )
            </if>
            <if test="param.contractTitle != null and param.contractTitle != ''">
                AND
                ( c.contract_title LIKE CONCAT('%', #{param.contractTitle}, '%')
                OR EXISTS (
                SELECT 1
                FROM ais_institution_center.m_institution_provider mip
                WHERE mip.id = c.fk_institution_provider_id
                AND (mip.name LIKE CONCAT('%', #{param.contractTitle}, '%')
                OR mip.name_chn LIKE CONCAT('%', #{param.contractTitle}, '%'))
                )
                OR EXISTS (
                SELECT 1
                FROM m_institution_provider_commission_plan cp
                JOIN r_institution_provider_commission_plan_institution cpi
                ON cpi.fk_institution_provider_commission_plan_id = cp.id
                JOIN ais_institution_center.m_institution mi
                ON mi.id = cpi.fk_institution_id
                WHERE cp.fk_institution_provider_contract_id = c.id
                AND (mi.name LIKE CONCAT('%', #{param.contractTitle}, '%')
                OR mi.name_chn LIKE CONCAT('%', #{param.contractTitle}, '%'))
                )
                OR EXISTS (
                SELECT 1
                FROM ais_pmp2_center.m_institution_provider_commission_plan cp
                WHERE cp.fk_institution_provider_contract_id = c.id
                AND cp.name LIKE CONCAT('%', #{param.contractTitle}, '%')
                and cp.id in
                <foreach collection="planIds" item="planId" separator="," open="(" close=")">
                    #{planId}
                </foreach>
                )
                )
            </if>
            <!--学校类型-->
            <if test="param.institutionTypeId != null and param.institutionTypeId > 0 ">
                and exists (
                    select 1 from ais_pmp2_center.m_institution_provider_commission_plan cp
                    inner join ais_pmp2_center.r_institution_provider_commission_plan_institution cpi on cpi.fk_institution_provider_commission_plan_id = cp.id
                    inner join ais_institution_center.m_institution mi on mi.id = cpi.fk_institution_id
                    where cp.fk_institution_provider_contract_id = c.id and mi.fk_institution_type_id =
                #{param.institutionTypeId}
                )
            </if>
            <!-- 审批记录包含指定审核人-->
            <if test="param.staffId != null and param.staffId > 0">
                and exists (
                    select 1 from ais_pmp2_center.m_institution_provider_commission_plan cp
                    inner join ais_pmp2_center.m_institution_provider_commission_plan_approval cpa on find_in_set(cp.id, cpa.fk_institution_provider_commission_plan_ids) > 0
                    where cp.fk_institution_provider_contract_id = c.id and cpa.approval_status in (2,3) and cpa.fk_staff_id =
                #{param.staffId}
                )
            </if>
<!--            <if test="param.isRenewal != null">-->
<!--                <choose>-->
<!--                    &lt;!&ndash; 续约中 &ndash;&gt;-->
<!--                    <when test="param.isRenewal == 1">-->
<!--                        AND EXISTS (-->
<!--                        SELECT 1-->
<!--                        FROM m_institution_provider_commission_plan cp-->
<!--                        WHERE cp.fk_institution_provider_contract_id = c.id-->
<!--                        AND cp.is_renewal = 1-->
<!--                        and cp.id in-->
<!--                        <foreach collection="planIds" item="planId" separator="," open="(" close=")">-->
<!--                            #{planId}-->
<!--                        </foreach>-->
<!--                        )-->
<!--                    </when>-->
<!--                    &lt;!&ndash; 未续约（包含没有方案的情况） &ndash;&gt;-->
<!--                    <when test="param.isRenewal == 0">-->
<!--                        AND NOT EXISTS (-->
<!--                        SELECT 1-->
<!--                        FROM m_institution_provider_commission_plan cp-->
<!--                        WHERE cp.fk_institution_provider_contract_id = c.id-->
<!--                        AND cp.is_renewal = 1-->
<!--                        and cp.id in-->
<!--                        <foreach collection="planIds" item="planId" separator="," open="(" close=")">-->
<!--                            #{planId}-->
<!--                        </foreach>-->
<!--                        )-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
            <!-- 适用地区-->
            <if test="param.territoryIds != null and param.territoryIds.size() > 0 ">
                and exists (
                SELECT 1 FROM m_institution_provider_commission_plan cp WHERE cp.fk_institution_provider_contract_id =
                c.id
                AND cp.id in
                <foreach collection="planIds" item="planId" separator="," open="(" close=")">
                    #{planId}
                </foreach>
                AND cp.id in
                <foreach collection="territoryPlanIds" item="territoryPlanId" separator="," open="(" close=")">
                    #{territoryPlanId}
                </foreach>
                )
            </if>
            <if test="param.renewalStatus != null">
                AND c.renewal_status =
                #{param.renewalStatus}
            </if>
            <!-- 最新审批记录等于指定审核人-->
            <!--            <if test="param.staffId != null and param.staffId > 0">-->
            <!--                AND EXISTS (SELECT 1 FROM m_age nt_commission_plan_approval pa WHERE pa.fk_agent_commission_plan_id = p.id-->
            <!--                AND pa.fk_staff_id = #{param.staffId}-->
            <!--                AND pa.approval_status IN (2, 3)-->
            <!--                AND NOT EXISTS (-->
            <!--                SELECT 1-->
            <!--                FROM m_agent_commission_plan_approval pa2-->
            <!--                WHERE pa2.fk_agent_commission_plan_id = pa.fk_agent_commission_plan_id-->
            <!--                AND pa2.approval_time &gt; pa.approval_time)-->
            <!--                )-->
            <!--            </if>-->
        </where>
        GROUP BY c.id
        order by c.id desc
    </select>

    <select id="getProviderContractById" resultType="com.get.pmpcenter.vo.institution.ProviderContractVo">
        select c.id,
               c.fk_institution_provider_id,
               c.fk_contract_party_id,
               c.contract_num,
               c.contract_title,
               c.start_time,
               c.end_time,
               c.is_timeless,
               c.remark,
               c.gmt_create_user,
               c.is_locked,
               c.is_active,
               c.approval_status,
               c.contract_kpi,
               c.renewal_status,
               c.renewal_remark,
               p.name     as contractPartyName,
               p.name_chn as contractPartyNameChn,
               i.name     as providerName,
               i.name_chn as providerNameChn
        from m_institution_provider_contract c
                 left join m_contract_party p on c.fk_contract_party_id = p.id
                 left join ais_institution_center.m_institution_provider i on c.fk_institution_provider_id = i.id
        where c.id = #{id}
    </select>

    <select id="selectContractIdsByCountryIds" resultType="java.lang.Long">
        select id from m_institution_provider_contract
        <where>
            fk_institution_provider_id in(
            SELECT DISTINCT (ipi.fk_institution_provider_id)
            FROM
            ais_institution_center.r_institution_provider_institution ipi
            INNER JOIN ais_institution_center.m_institution m ON ipi.fk_institution_id = m.id
            WHERE ipi.is_active = 1
            <!-- AND m.is_active = 1-->
            and m.fk_area_country_id in
            <foreach collection="countryIds" item="countryId" separator="," open="(" close=")">
                #{countryId}
            </foreach>
            )
        </where>
    </select>

    <select id="selectExpireContract" resultType="com.get.pmpcenter.entity.InstitutionProviderContract">
        SELECT c.* FROM m_institution_provider_contract c
        WHERE c.is_timeless = 0
        AND DATE(c.end_time) &lt; CURRENT_DATE()
        AND c.is_active = 1
        AND c.renewal_status != 1
        <if test="institutionProviderId != null and institutionProviderId > 0">
            AND fk_institution_provider_id =
            #{institutionProviderId}
        </if>
    </select>
</mapper>