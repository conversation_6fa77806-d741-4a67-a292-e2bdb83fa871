package com.get.workflowcenter.service.impl;


import com.get.common.utils.BeanCopyUtils;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.service.ApprovalRecordsService;
import com.get.workflowcenter.service.IAiService;
import com.get.workflowcenter.vo.AiApprovalRecordListVo;
import com.get.workflowcenter.vo.ApprovalRecordListVo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AiServiceImpl implements IAiService {
    @Resource
    private ApprovalRecordsService approvalRecordsService;

    /**
     * 获取我的所有审批
     * @return
     */
    @Override
    public List<AiApprovalRecordListVo> getMyApproval() {
        List<AiApprovalRecordListVo> aiApprovalRecordListVos = new ArrayList<>();
        ApprovalRecordDto approvalRecordDto = new ApprovalRecordDto();
        //状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
        approvalRecordDto.setApprovalStatus(2);
        //审批类型 myApproval 所有审批 m_leave_application_form 工休申请单 m_payment_application_form 支付申请单
        // m_prepay_application_form 借款申请单 m_expense_claim_form 费用报销单 m_travel_claim_form 差旅报销单
        approvalRecordDto.setApprovalTab("myApproval");
        List<ApprovalRecordListVo> datas = approvalRecordsService.datas(approvalRecordDto, null);
        if (GeneralTool.isEmpty(datas)) {
            return Collections.emptyList();
        }
        for (ApprovalRecordListVo approvalRecordListVo : datas) {
            AiApprovalRecordListVo aiApprovalRecordListVo = BeanCopyUtils.objClone(approvalRecordListVo, AiApprovalRecordListVo::new);
            if (GeneralTool.isNotEmpty(approvalRecordListVo.getBusinessKey())) {
                if ("m_leave_application_form".equals(approvalRecordListVo.getBusinessKey())) {
                    aiApprovalRecordListVo.setBusinessKeyName("工休申请单");
                }
                else if ("m_payment_application_form".equals(approvalRecordListVo.getBusinessKey())) {
                    aiApprovalRecordListVo.setBusinessKeyName("支付申请单");
                }
                else if ("m_prepay_application_form".equals(approvalRecordListVo.getBusinessKey())) {
                    aiApprovalRecordListVo.setBusinessKeyName("借款申请单");
                }
                else if ("m_expense_claim_form".equals(approvalRecordListVo.getBusinessKey())) {
                    aiApprovalRecordListVo.setBusinessKeyName("费用报销单");
                }
                else if ("m_travel_claim_form".equals(approvalRecordListVo.getBusinessKey())){
                    aiApprovalRecordListVo.setBusinessKeyName("差旅申请单");
                }
            }
            //            0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
            if (GeneralTool.isNotEmpty(approvalRecordListVo.getStatus())) {
                Integer status = approvalRecordListVo.getStatus();
                if (status == 0) {
                    aiApprovalRecordListVo.setStatus("待发起");
                }
                if (status == 1) {
                    aiApprovalRecordListVo.setStatus("审批结束");
                }
                if (status == 2) {
                    aiApprovalRecordListVo.setStatus("审批中");
                }
                if (status == 3) {
                    aiApprovalRecordListVo.setStatus("审批拒绝");
                }
                if (status == 4) {
                    aiApprovalRecordListVo.setStatus("申请放弃");
                }
                if (status == 5) {
                    aiApprovalRecordListVo.setStatus("作废");
                }
                if (status == 6) {
                    aiApprovalRecordListVo.setStatus("撤销");
                }
            }
            aiApprovalRecordListVos.add(aiApprovalRecordListVo);

        }
        //过滤没有审批人的数据
        aiApprovalRecordListVos.removeIf(approvalRecordListVo -> GeneralTool.isEmpty(approvalRecordListVo.getCurrentApproverName()));
        return aiApprovalRecordListVos;

    }
}
